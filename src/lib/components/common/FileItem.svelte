<script lang="ts">
	import { createEventDispatcher, getContext } from 'svelte';
	import { formatFileSize } from '$lib/utils';

	import FileItemModal from './FileItemModal.svelte';
	import Spinner from './Spinner.svelte';
	import Tooltip from './Tooltip.svelte';

	const i18n = getContext('i18n');
	const dispatch = createEventDispatcher();

	export let className = 'w-70';
	export let colorClassName = 'bg-white dark:bg-gray-850 border border-gray-50 dark:border-white/5';
	export let url: string | null = null;

	export let dismissible = false;
	export let loading = false;

	export let item = null;
	export let edit = false;
	export let small = false;

	export let name: string;
	export let type: string;
	export let size: number;

	let showModal = false;
</script>

{#if item}
	<FileItemModal bind:show={showModal} bind:item {edit} />
{/if}

<div class="relative">
	<button
		class="p-1.5 {className} flex items-center gap-1 {colorClassName} {small
			? 'rounded-xl'
			: 'rounded-2xl'} text-left"
		type="button"
		on:click={async () => {
			if (item?.file?.data?.content) {
				showModal = !showModal;
			} else {
				if (url) {
					if (type === 'file') {
						window.open(`${url}/content`, '_blank')?.focus();
					} else {
						window.open(`${url}`, '_blank')?.focus();
					}
				}
			}

			dispatch('click');
		}}
	>
		{#if !small}
			<div class="p-3 bg-black/20 dark:bg-white/10 text-white rounded-xl">
				{#if !loading}
					<svg
						xmlns="http://www.w3.org/2000/svg"
						viewBox="0 0 24 24"
						fill="currentColor"
						class=" size-5"
					>
						<path
							fill-rule="evenodd"
							d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625ZM7.5 15a.75.75 0 0 1 .75-.75h7.5a.75.75 0 0 1 0 1.5h-7.5A.75.75 0 0 1 7.5 15Zm.75 2.25a.75.75 0 0 0 0 1.5H12a.75.75 0 0 0 0-1.5H8.25Z"
							clip-rule="evenodd"
						/>
						<path
							d="M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z"
						/>
					</svg>
				{:else}
					<Spinner />
				{/if}
			</div>
		{/if}

		{#if !small}
			<div class="flex flex-col justify-center -space-y-0.5 px-2.5 w-full">
				<div class=" dark:text-gray-100 text-sm font-medium truncate max-w-[200px] min-w-0 mb-1">
					{name}
				</div>

				<div class=" flex justify-between text-gray-600 dark:text-gray-500 text-xs line-clamp-1">
					{#if type === 'file'}
						{$i18n.t('File')}
					{:else if type === 'doc'}
						{$i18n.t('Document')}
					{:else if type === 'collection'}
						{$i18n.t('Collection')}
					{:else}
						<span class=" capitalize line-clamp-1">{type}</span>
					{/if}
					{#if size}
						<span class="capitalize">{formatFileSize(size)}</span>
					{/if}
				</div>
			</div>
		{:else}
			<Tooltip content={name} className="flex flex-col w-full" placement="top-start">
				<div class="flex flex-col justify-center -space-y-0.5 px-2.5 w-full">
					<div class=" dark:text-gray-100 text-sm flex justify-between items-center">
						{#if loading}
							<div class=" shrink-0 mr-2">
								<Spinner className="size-4" />
							</div>
						{/if}
						<div class="font-medium truncate max-w-[180px] min-w-0">{name}</div>
						<div class="text-gray-600 dark:text-gray-500 text-xs capitalize shrink-0">
							{formatFileSize(size)}
						</div>
					</div>
				</div>
			</Tooltip>
		{/if}
	</button>
	{#if dismissible}
		<button
			aria-label="Remove {name}"
			class="absolute -top-1 -right-1 bg-gray-400 text-white border border-white rounded-full transition"
			type="button"
			on:click|stopPropagation={() => {
				dispatch('dismiss');
			}}
		>
			<svg
				xmlns="http://www.w3.org/2000/svg"
				viewBox="0 0 20 20"
				fill="currentColor"
				class="w-4 h-4"
			>
				<path
					d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z"
				/>
			</svg>
		</button>
	{/if}
</div>
